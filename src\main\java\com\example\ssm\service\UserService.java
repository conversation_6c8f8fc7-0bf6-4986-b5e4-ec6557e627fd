package com.example.ssm.service;

import com.example.ssm.entity.User;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 用户登录验证
     * @param username 用户名
     * @param password 密码
     * @return 用户对象，如果验证失败返回null
     */
    User login(String username, String password);
    
    /**
     * 用户注册
     * @param user 用户对象
     * @return 注册结果，true表示成功，false表示失败
     */
    boolean register(User user);
    
    /**
     * 检查用户名是否已存在
     * @param username 用户名
     * @return true表示已存在，false表示不存在
     */
    boolean isUsernameExists(String username);
    
    /**
     * 根据ID查询用户
     * @param id 用户ID
     * @return 用户对象
     */
    User findById(Integer id);
    
    /**
     * 更新用户信息
     * @param user 用户对象
     * @return 更新结果
     */
    boolean updateUser(User user);
}
