package com.example.ssm.controller;

import com.example.ssm.entity.User;
import com.example.ssm.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpSession;

/**
 * 登录控制器
 */
@Controller
public class LoginController {
    
    // 可以切换使用XML配置或注解配置的Service
    @Autowired
    @Qualifier("userService") // XML配置方式
    // @Qualifier("userAnnotationService") // 注解配置方式
    private UserService userService;
    
    /**
     * 显示登录页面
     */
    @GetMapping("/login")
    public String showLoginPage(Model model) {
        model.addAttribute("user", new User());
        return "login";
    }
    
    /**
     * 处理登录请求
     */
    @PostMapping("/login")
    public String login(@ModelAttribute("user") User user, 
                       Model model, 
                       HttpSession session) {
        
        // 验证用户输入
        if (user.getUsername() == null || user.getUsername().trim().isEmpty()) {
            model.addAttribute("error", "用户名不能为空");
            return "login";
        }
        
        if (user.getPassword() == null || user.getPassword().trim().isEmpty()) {
            model.addAttribute("error", "密码不能为空");
            return "login";
        }
        
        // 验证用户凭据
        User loginUser = userService.login(user.getUsername().trim(), user.getPassword());
        
        if (loginUser != null) {
            // 登录成功，创建会话
            session.setAttribute("currentUser", loginUser);
            return "redirect:/home";
        } else {
            // 登录失败，回显数据并显示错误信息
            model.addAttribute("error", "用户名或密码错误");
            model.addAttribute("username", user.getUsername());
            model.addAttribute("password", user.getPassword());
            return "login";
        }
    }
    
    /**
     * 注销登录
     */
    @GetMapping("/logout")
    public String logout(HttpSession session) {
        session.invalidate();
        return "redirect:/login";
    }
    
    /**
     * 首页重定向到登录页面
     */
    @GetMapping("/")
    public String index() {
        return "redirect:/login";
    }
}
