<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - SSM Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }
        .register-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 500px;
        }
        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .register-body {
            padding: 30px;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            margin-bottom: 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            width: 100%;
            margin-bottom: 15px;
        }
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .alert {
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .login-link {
            text-align: center;
            margin-top: 20px;
        }
        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        .login-link a:hover {
            text-decoration: underline;
        }
        .invalid-feedback {
            display: block;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <h2><i class="fas fa-user-plus"></i> 用户注册</h2>
            <p class="mb-0">创建您的账号</p>
        </div>
        <div class="register-body">
            <!-- 显示错误消息 -->
            <c:if test="${not empty error}">
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-circle"></i> ${error}
                </div>
            </c:if>
            
            <form action="${pageContext.request.contextPath}/register" method="post">
                <div class="row">
                    <div class="col-12">
                        <label for="username" class="form-label">
                            <i class="fas fa-user"></i> 用户名 *
                        </label>
                        <input type="text" class="form-control" id="username" name="username" 
                               value="${user.username}" placeholder="请输入用户名（3-20个字符）" 
                               minlength="3" maxlength="20" required>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock"></i> 密码 *
                        </label>
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="请输入密码（6-20个字符）" 
                               minlength="6" maxlength="20" required>
                    </div>
                    <div class="col-md-6">
                        <label for="confirmPassword" class="form-label">
                            <i class="fas fa-lock"></i> 确认密码 *
                        </label>
                        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" 
                               placeholder="请再次输入密码" required>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <label for="phone" class="form-label">
                            <i class="fas fa-phone"></i> 手机号码
                        </label>
                        <input type="tel" class="form-control" id="phone" name="phone" 
                               value="${user.phone}" placeholder="请输入手机号码" 
                               pattern="^1[3-9]\d{9}$">
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <label for="avatar" class="form-label">
                            <i class="fas fa-image"></i> 头像地址
                        </label>
                        <input type="url" class="form-control" id="avatar" name="avatar" 
                               value="${user.avatar}" placeholder="请输入头像图片地址（可选）">
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary btn-register">
                    <i class="fas fa-user-plus"></i> 注册
                </button>
            </form>
            
            <div class="login-link">
                <p>已有账号？ <a href="${pageContext.request.contextPath}/login">立即登录</a></p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 密码确认验证
        document.getElementById('confirmPassword').addEventListener('input', function() {
            var password = document.getElementById('password').value;
            var confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('两次输入的密码不一致');
            } else {
                this.setCustomValidity('');
            }
        });
        
        document.getElementById('password').addEventListener('input', function() {
            var confirmPassword = document.getElementById('confirmPassword');
            if (confirmPassword.value) {
                confirmPassword.dispatchEvent(new Event('input'));
            }
        });
    </script>
</body>
</html>
