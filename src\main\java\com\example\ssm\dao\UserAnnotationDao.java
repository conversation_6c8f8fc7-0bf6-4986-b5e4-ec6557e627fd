package com.example.ssm.dao;

import com.example.ssm.entity.User;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

/**
 * 用户数据访问接口 - 注解配置方式
 */
@Repository
@Mapper
public interface UserAnnotationDao {
    
    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM users WHERE username = #{username}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "username", column = "username"),
        @Result(property = "password", column = "password"),
        @Result(property = "phone", column = "phone"),
        @Result(property = "avatar", column = "avatar"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    User findByUsername(@Param("username") String username);
    
    /**
     * 根据用户名和密码查询用户（登录验证）
     */
    @Select("SELECT * FROM users WHERE username = #{username} AND password = #{password}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "username", column = "username"),
        @Result(property = "password", column = "password"),
        @Result(property = "phone", column = "phone"),
        @Result(property = "avatar", column = "avatar"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    User findByUsernameAndPassword(@Param("username") String username, @Param("password") String password);
    
    /**
     * 插入新用户
     */
    @Insert("INSERT INTO users(username, password, phone, avatar, create_time, update_time) " +
            "VALUES(#{username}, #{password}, #{phone}, #{avatar}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUser(User user);
    
    /**
     * 根据ID查询用户
     */
    @Select("SELECT * FROM users WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "username", column = "username"),
        @Result(property = "password", column = "password"),
        @Result(property = "phone", column = "phone"),
        @Result(property = "avatar", column = "avatar"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    User findById(@Param("id") Integer id);
    
    /**
     * 更新用户信息
     */
    @Update("UPDATE users SET username = #{username}, password = #{password}, " +
            "phone = #{phone}, avatar = #{avatar}, update_time = NOW() WHERE id = #{id}")
    int updateUser(User user);
    
    /**
     * 删除用户
     */
    @Delete("DELETE FROM users WHERE id = #{id}")
    int deleteUser(@Param("id") Integer id);
}
