@echo off
echo ========================================
echo SSM项目启动调试脚本
echo ========================================

echo.
echo 1. 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误: Java未正确安装或配置
    pause
    exit /b 1
)

echo.
echo 2. 检查JAVA_HOME...
if "%JAVA_HOME%"=="" (
    echo 警告: JAVA_HOME未设置
) else (
    echo JAVA_HOME: %JAVA_HOME%
)

echo.
echo 3. 检查Maven...
mvn -version
if %errorlevel% neq 0 (
    echo 错误: Maven未正确安装或配置
    pause
    exit /b 1
)

echo.
echo 4. 清理并编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)

echo.
echo 5. 打包项目...
mvn package -DskipTests
if %errorlevel% neq 0 (
    echo 错误: 项目打包失败
    pause
    exit /b 1
)

echo.
echo 6. 检查WAR包...
if exist "target\ssm-demo.war" (
    echo 成功: WAR包已生成
    dir target\ssm-demo.war
) else (
    echo 错误: WAR包未生成
    pause
    exit /b 1
)

echo.
echo 7. 检查Tomcat环境...
if "%CATALINA_HOME%"=="" (
    echo 错误: CATALINA_HOME未设置
    echo 请设置CATALINA_HOME环境变量指向Tomcat安装目录
    pause
    exit /b 1
) else (
    echo CATALINA_HOME: %CATALINA_HOME%
)

echo.
echo 8. 检查Tomcat安装...
if exist "%CATALINA_HOME%\bin\startup.bat" (
    echo 成功: Tomcat安装正常
) else (
    echo 错误: Tomcat安装不完整
    pause
    exit /b 1
)

echo.
echo 9. 检查端口占用...
netstat -ano | findstr :8080
if %errorlevel% equ 0 (
    echo 警告: 端口8080已被占用
    echo 请关闭占用端口的程序或修改Tomcat端口
)

echo.
echo 10. 停止现有Tomcat进程...
taskkill /f /im java.exe 2>nul
timeout /t 2 >nul

echo.
echo 11. 部署应用到Tomcat...
if exist "%CATALINA_HOME%\webapps\ssm-demo.war" (
    del "%CATALINA_HOME%\webapps\ssm-demo.war"
)
if exist "%CATALINA_HOME%\webapps\ssm-demo" (
    rmdir /s /q "%CATALINA_HOME%\webapps\ssm-demo"
)
copy "target\ssm-demo.war" "%CATALINA_HOME%\webapps\"
if %errorlevel% neq 0 (
    echo 错误: WAR包部署失败
    pause
    exit /b 1
)

echo.
echo 12. 启动Tomcat...
echo 正在启动Tomcat服务器...
start "Tomcat" "%CATALINA_HOME%\bin\startup.bat"

echo.
echo 13. 等待服务器启动...
timeout /t 10 >nul

echo.
echo 14. 检查服务器状态...
netstat -ano | findstr :8080
if %errorlevel% equ 0 (
    echo 成功: Tomcat已启动，监听端口8080
) else (
    echo 警告: Tomcat可能未正常启动
)

echo.
echo ========================================
echo 启动完成！
echo ========================================
echo.
echo 访问地址:
echo http://localhost:8080/ssm-demo/
echo http://localhost:8080/ssm-demo/login
echo.
echo 测试账号:
echo 用户名: admin  密码: 123456
echo 用户名: test   密码: 123456
echo 用户名: demo   密码: 123456
echo.
echo 如果无法访问，请检查:
echo 1. Tomcat日志: %CATALINA_HOME%\logs\catalina.*.log
echo 2. 应用日志: %CATALINA_HOME%\logs\localhost.*.log
echo 3. MySQL服务是否启动
echo 4. 数据库是否已创建和初始化
echo.
pause
