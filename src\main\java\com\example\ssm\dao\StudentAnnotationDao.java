package com.example.ssm.dao;

import com.example.ssm.entity.Student;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 学生数据访问接口 - 注解配置方式
 */
@Repository
@Mapper
public interface StudentAnnotationDao {
    
    /**
     * 查询所有学生
     */
    @Select("SELECT * FROM students ORDER BY create_time DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "studentId", column = "student_id"),
        @Result(property = "name", column = "name"),
        @Result(property = "gender", column = "gender"),
        @Result(property = "age", column = "age"),
        @Result(property = "major", column = "major"),
        @Result(property = "className", column = "class_name"),
        @Result(property = "phone", column = "phone"),
        @Result(property = "email", column = "email"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    List<Student> findAll();
    
    /**
     * 根据关键词搜索学生
     */
    @Select("SELECT * FROM students WHERE name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR student_id LIKE CONCAT('%', #{keyword}, '%') " +
            "OR major LIKE CONCAT('%', #{keyword}, '%') " +
            "OR class_name LIKE CONCAT('%', #{keyword}, '%') " +
            "ORDER BY create_time DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "studentId", column = "student_id"),
        @Result(property = "name", column = "name"),
        @Result(property = "gender", column = "gender"),
        @Result(property = "age", column = "age"),
        @Result(property = "major", column = "major"),
        @Result(property = "className", column = "class_name"),
        @Result(property = "phone", column = "phone"),
        @Result(property = "email", column = "email"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    List<Student> searchStudents(@Param("keyword") String keyword);
    
    /**
     * 根据ID查询学生
     */
    @Select("SELECT * FROM students WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "studentId", column = "student_id"),
        @Result(property = "name", column = "name"),
        @Result(property = "gender", column = "gender"),
        @Result(property = "age", column = "age"),
        @Result(property = "major", column = "major"),
        @Result(property = "className", column = "class_name"),
        @Result(property = "phone", column = "phone"),
        @Result(property = "email", column = "email"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    Student findById(@Param("id") Integer id);
    
    /**
     * 根据学号查询学生
     */
    @Select("SELECT * FROM students WHERE student_id = #{studentId}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "studentId", column = "student_id"),
        @Result(property = "name", column = "name"),
        @Result(property = "gender", column = "gender"),
        @Result(property = "age", column = "age"),
        @Result(property = "major", column = "major"),
        @Result(property = "className", column = "class_name"),
        @Result(property = "phone", column = "phone"),
        @Result(property = "email", column = "email"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    Student findByStudentId(@Param("studentId") String studentId);
    
    /**
     * 插入新学生
     */
    @Insert("INSERT INTO students(student_id, name, gender, age, major, class_name, phone, email, create_time, update_time) " +
            "VALUES(#{studentId}, #{name}, #{gender}, #{age}, #{major}, #{className}, #{phone}, #{email}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertStudent(Student student);
    
    /**
     * 更新学生信息
     */
    @Update("UPDATE students SET student_id = #{studentId}, name = #{name}, gender = #{gender}, " +
            "age = #{age}, major = #{major}, class_name = #{className}, phone = #{phone}, " +
            "email = #{email}, update_time = NOW() WHERE id = #{id}")
    int updateStudent(Student student);
    
    /**
     * 删除学生
     */
    @Delete("DELETE FROM students WHERE id = #{id}")
    int deleteStudent(@Param("id") Integer id);
    
    /**
     * 分页查询学生
     */
    @Select("SELECT * FROM students ORDER BY create_time DESC LIMIT #{offset}, #{limit}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "studentId", column = "student_id"),
        @Result(property = "name", column = "name"),
        @Result(property = "gender", column = "gender"),
        @Result(property = "age", column = "age"),
        @Result(property = "major", column = "major"),
        @Result(property = "className", column = "class_name"),
        @Result(property = "phone", column = "phone"),
        @Result(property = "email", column = "email"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    List<Student> findByPage(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 统计学生总数
     */
    @Select("SELECT COUNT(*) FROM students")
    int countStudents();
}
