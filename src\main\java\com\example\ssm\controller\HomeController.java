package com.example.ssm.controller;

import com.example.ssm.entity.Student;
import com.example.ssm.entity.User;
import com.example.ssm.service.StudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpSession;
import java.util.List;

/**
 * 主页控制器
 */
@Controller
public class HomeController {
    
    @Autowired
    @Qualifier("studentService")
    private StudentService studentService;
    
    /**
     * 显示主页
     */
    @GetMapping("/home")
    public String home(HttpSession session, Model model) {
        User currentUser = (User) session.getAttribute("currentUser");
        if (currentUser == null) {
            return "redirect:/login";
        }
        
        model.addAttribute("currentUser", currentUser);
        model.addAttribute("activeTab", "home");
        return "home";
    }
    
    /**
     * 个人信息页面
     */
    @GetMapping("/profile")
    public String profile(HttpSession session, Model model) {
        User currentUser = (User) session.getAttribute("currentUser");
        if (currentUser == null) {
            return "redirect:/login";
        }
        
        model.addAttribute("currentUser", currentUser);
        model.addAttribute("activeTab", "profile");
        return "home";
    }
    
    /**
     * 学生信息页面
     */
    @GetMapping("/students")
    public String students(HttpSession session, Model model,
                          @RequestParam(value = "keyword", required = false) String keyword) {
        User currentUser = (User) session.getAttribute("currentUser");
        if (currentUser == null) {
            return "redirect:/login";
        }
        
        List<Student> students;
        if (keyword != null && !keyword.trim().isEmpty()) {
            students = studentService.searchStudents(keyword.trim());
            model.addAttribute("keyword", keyword.trim());
        } else {
            students = studentService.findAll();
        }
        
        model.addAttribute("currentUser", currentUser);
        model.addAttribute("students", students);
        model.addAttribute("activeTab", "students");
        return "home";
    }
    
    /**
     * 关于我们页面
     */
    @GetMapping("/about")
    public String about(HttpSession session, Model model) {
        User currentUser = (User) session.getAttribute("currentUser");
        if (currentUser == null) {
            return "redirect:/login";
        }
        
        model.addAttribute("currentUser", currentUser);
        model.addAttribute("activeTab", "about");
        return "home";
    }
    
    /**
     * AJAX搜索学生
     */
    @GetMapping("/api/students/search")
    @ResponseBody
    public List<Student> searchStudentsAjax(@RequestParam("keyword") String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return studentService.findAll();
        }
        return studentService.searchStudents(keyword.trim());
    }
}
