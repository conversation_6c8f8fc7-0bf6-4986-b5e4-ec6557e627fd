<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="
           http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context.xsd
           http://www.springframework.org/schema/mvc
           http://www.springframework.org/schema/mvc/spring-mvc.xsd">

    <!-- 扫描Controller层组件 -->
    <context:component-scan base-package="com.example.ssm.controller" />
    
    <!-- 启用Spring MVC注解驱动 -->
    <mvc:annotation-driven />
    
    <!-- 配置视图解析器 -->
    <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="prefix" value="/WEB-INF/views/" />
        <property name="suffix" value=".jsp" />
    </bean>
    
    <!-- 静态资源映射 -->
    <mvc:resources mapping="/css/**" location="/WEB-INF/static/css/" />
    <mvc:resources mapping="/js/**" location="/WEB-INF/static/js/" />
    <mvc:resources mapping="/images/**" location="/WEB-INF/static/images/" />
    <mvc:resources mapping="/fonts/**" location="/WEB-INF/static/fonts/" />
    
    <!-- 配置文件上传解析器 -->
    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="maxUploadSize" value="10485760" /> <!-- 10MB -->
        <property name="defaultEncoding" value="UTF-8" />
    </bean>
    
    <!-- 配置拦截器 -->
    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/home/<USER>" />
            <mvc:mapping path="/profile/**" />
            <mvc:mapping path="/students/**" />
            <mvc:mapping path="/about/**" />
            <mvc:mapping path="/api/**" />
            <bean class="com.example.ssm.interceptor.LoginInterceptor" />
        </mvc:interceptor>
    </mvc:interceptors>
    
</beans>
