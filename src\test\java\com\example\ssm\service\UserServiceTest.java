package com.example.ssm.service;

import com.example.ssm.entity.User;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 * 用户服务测试类
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring/applicationContext.xml"})
public class UserServiceTest {
    
    @Autowired
    @Qualifier("userService")
    private UserService userService;
    
    @Test
    public void testLogin() {
        // 测试正确的用户名和密码
        User user = userService.login("admin", "123456");
        assertNotNull("登录应该成功", user);
        assertEquals("用户名应该匹配", "admin", user.getUsername());
        
        // 测试错误的密码
        User wrongUser = userService.login("admin", "wrongpassword");
        assertNull("错误密码应该登录失败", wrongUser);
        
        // 测试不存在的用户
        User nonExistUser = userService.login("nonexist", "123456");
        assertNull("不存在的用户应该登录失败", nonExistUser);
    }
    
    @Test
    public void testIsUsernameExists() {
        // 测试存在的用户名
        boolean exists = userService.isUsernameExists("admin");
        assertTrue("admin用户应该存在", exists);
        
        // 测试不存在的用户名
        boolean notExists = userService.isUsernameExists("nonexistuser");
        assertFalse("不存在的用户名应该返回false", notExists);
    }
}
