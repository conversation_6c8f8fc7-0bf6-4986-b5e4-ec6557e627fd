-- 创建数据库
CREATE DATABASE IF NOT EXISTS ssm_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE ssm_demo;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(255) COMMENT '头像地址',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 创建学生表
CREATE TABLE IF NOT EXISTS students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) NOT NULL UNIQUE COMMENT '学号',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    gender ENUM('男', '女') NOT NULL COMMENT '性别',
    age INT NOT NULL COMMENT '年龄',
    major VARCHAR(100) NOT NULL COMMENT '专业',
    class_name VARCHAR(50) NOT NULL COMMENT '班级',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生表';

-- 插入测试用户数据
INSERT INTO users (username, password, phone, avatar) VALUES
('admin', '123456', '13800138000', '/images/admin-avatar.png'),
('test', '123456', '13900139000', '/images/test-avatar.png'),
('demo', '123456', '13700137000', '/images/demo-avatar.png');

-- 插入测试学生数据
INSERT INTO students (student_id, name, gender, age, major, class_name, phone, email) VALUES
('2021001', '张三', '男', 20, '计算机科学与技术', '计科2101', '13800001001', '<EMAIL>'),
('2021002', '李四', '女', 19, '计算机科学与技术', '计科2101', '13800001002', '<EMAIL>'),
('2021003', '王五', '男', 21, '软件工程', '软工2101', '13800001003', '<EMAIL>'),
('2021004', '赵六', '女', 20, '软件工程', '软工2101', '13800001004', '<EMAIL>'),
('2021005', '钱七', '男', 19, '网络工程', '网工2101', '13800001005', '<EMAIL>'),
('2021006', '孙八', '女', 20, '网络工程', '网工2101', '13800001006', '<EMAIL>'),
('2021007', '周九', '男', 21, '信息安全', '信安2101', '13800001007', '<EMAIL>'),
('2021008', '吴十', '女', 19, '信息安全', '信安2101', '13800001008', '<EMAIL>'),
('2021009', '郑一', '男', 20, '数据科学与大数据技术', '数据2101', '13800001009', '<EMAIL>'),
('2021010', '王二', '女', 21, '数据科学与大数据技术', '数据2101', '13800001010', '<EMAIL>'),
('2021011', '李三', '男', 19, '人工智能', 'AI2101', '13800001011', '<EMAIL>'),
('2021012', '张四', '女', 20, '人工智能', 'AI2101', '13800001012', '<EMAIL>'),
('2021013', '刘五', '男', 21, '物联网工程', '物联2101', '13800001013', '<EMAIL>'),
('2021014', '陈六', '女', 19, '物联网工程', '物联2101', '13800001014', '<EMAIL>'),
('2021015', '杨七', '男', 20, '电子信息工程', '电信2101', '13800001015', '<EMAIL>'),
('2021016', '黄八', '女', 21, '电子信息工程', '电信2101', '13800001016', '<EMAIL>'),
('2021017', '林九', '男', 19, '通信工程', '通信2101', '13800001017', '<EMAIL>'),
('2021018', '何十', '女', 20, '通信工程', '通信2101', '13800001018', '<EMAIL>'),
('2021019', '罗一', '男', 21, '自动化', '自动2101', '13800001019', '<EMAIL>'),
('2021020', '高二', '女', 19, '自动化', '自动2101', '13800001020', '<EMAIL>');

-- 创建索引
CREATE INDEX idx_username ON users(username);
CREATE INDEX idx_student_id ON students(student_id);
CREATE INDEX idx_student_name ON students(name);
CREATE INDEX idx_student_major ON students(major);
CREATE INDEX idx_student_class ON students(class_name);
