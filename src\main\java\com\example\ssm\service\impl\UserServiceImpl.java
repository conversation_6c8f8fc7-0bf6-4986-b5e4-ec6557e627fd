package com.example.ssm.service.impl;

import com.example.ssm.dao.UserDao;
import com.example.ssm.entity.User;
import com.example.ssm.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户服务实现类 - XML配置方式
 */
@Service("userService")
@Transactional
public class UserServiceImpl implements UserService {
    
    @Autowired
    private UserDao userDao;
    
    @Override
    public User login(String username, String password) {
        if (username == null || password == null) {
            return null;
        }
        return userDao.findByUsernameAndPassword(username, password);
    }
    
    @Override
    public boolean register(User user) {
        if (user == null || user.getUsername() == null || user.getPassword() == null) {
            return false;
        }
        
        // 检查用户名是否已存在
        if (isUsernameExists(user.getUsername())) {
            return false;
        }
        
        try {
            int result = userDao.insertUser(user);
            return result > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public boolean isUsernameExists(String username) {
        if (username == null) {
            return false;
        }
        User existingUser = userDao.findByUsername(username);
        return existingUser != null;
    }
    
    @Override
    public User findById(Integer id) {
        if (id == null) {
            return null;
        }
        return userDao.findById(id);
    }
    
    @Override
    public boolean updateUser(User user) {
        if (user == null || user.getId() == null) {
            return false;
        }
        
        try {
            int result = userDao.updateUser(user);
            return result > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
