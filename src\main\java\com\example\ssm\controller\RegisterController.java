package com.example.ssm.controller;

import com.example.ssm.entity.User;
import com.example.ssm.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;

/**
 * 注册控制器
 */
@Controller
public class RegisterController {
    
    // 可以切换使用XML配置或注解配置的Service
    @Autowired
    @Qualifier("userService") // XML配置方式
    // @Qualifier("userAnnotationService") // 注解配置方式
    private UserService userService;
    
    /**
     * 显示注册页面
     */
    @GetMapping("/register")
    public String showRegisterPage(Model model) {
        model.addAttribute("user", new User());
        return "register";
    }
    
    /**
     * 处理注册请求
     */
    @PostMapping("/register")
    public String register(@Valid @ModelAttribute("user") User user,
                          BindingResult bindingResult,
                          @RequestParam("confirmPassword") String confirmPassword,
                          Model model) {
        
        // 验证表单数据
        if (bindingResult.hasErrors()) {
            return "register";
        }
        
        // 验证两次密码是否一致
        if (!user.getPassword().equals(confirmPassword)) {
            model.addAttribute("error", "两次输入的密码不一致");
            model.addAttribute("user", user);
            return "register";
        }
        
        // 检查用户名是否已存在
        if (userService.isUsernameExists(user.getUsername())) {
            model.addAttribute("error", "用户名已存在，请选择其他用户名");
            model.addAttribute("user", user);
            return "register";
        }
        
        // 处理头像信息（这里简单设置默认头像）
        if (user.getAvatar() == null || user.getAvatar().trim().isEmpty()) {
            user.setAvatar("/images/default-avatar.png");
        }
        
        // 注册用户
        boolean success = userService.register(user);
        
        if (success) {
            // 注册成功，将用户名和密码填充到登录界面
            model.addAttribute("successMessage", "注册成功！请使用您的账号登录。");
            model.addAttribute("username", user.getUsername());
            model.addAttribute("password", user.getPassword());
            model.addAttribute("user", new User());
            return "login";
        } else {
            // 注册失败
            model.addAttribute("error", "注册失败，请稍后重试");
            model.addAttribute("user", user);
            return "register";
        }
    }
}
