package com.example.ssm.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * 数据库配置类 - 注解配置方式
 */
@Configuration
@PropertySource("classpath:database.properties")
@MapperScan(basePackages = "com.example.ssm.dao", 
           sqlSessionFactoryRef = "annotationSqlSessionFactory")
@EnableTransactionManagement
public class DatabaseConfig {
    
    @Value("${jdbc.driver}")
    private String driverClassName;
    
    @Value("${jdbc.url}")
    private String url;
    
    @Value("${jdbc.username}")
    private String username;
    
    @Value("${jdbc.password}")
    private String password;
    
    /**
     * 配置数据源
     */
    @Bean
    public DataSource annotationDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName(driverClassName);
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        
        // 连接池配置
        dataSource.setInitialSize(5);
        dataSource.setMaxActive(20);
        dataSource.setMinIdle(5);
        dataSource.setMaxWait(60000);
        dataSource.setValidationQuery("SELECT 1");
        dataSource.setTestOnBorrow(true);
        dataSource.setTestWhileIdle(true);
        dataSource.setTimeBetweenEvictionRunsMillis(60000);
        
        return dataSource;
    }
    
    /**
     * 配置SqlSessionFactory
     */
    @Bean
    public SqlSessionFactory annotationSqlSessionFactory() throws Exception {
        SqlSessionFactoryBean factoryBean = new SqlSessionFactoryBean();
        factoryBean.setDataSource(annotationDataSource());
        
        // 设置MyBatis配置文件位置
        factoryBean.setConfigLocation(
            new PathMatchingResourcePatternResolver().getResource("classpath:mybatis/mybatis-config.xml"));
        
        return factoryBean.getObject();
    }
    
    /**
     * 配置事务管理器
     */
    @Bean
    public PlatformTransactionManager annotationTransactionManager() {
        return new DataSourceTransactionManager(annotationDataSource());
    }
}
