# SSM项目启动完整指南

## 🚀 快速启动步骤

### 第一步：环境检查
确保以下软件已正确安装：
- ✅ JDK 8+ (配置JAVA_HOME)
- ✅ Maven 3.6+ 
- ✅ MySQL 8.0+
- ✅ Tomcat 9.0+ (配置CATALINA_HOME)

### 第二步：数据库准备
```bash
# 1. 启动MySQL服务
net start MySQL

# 2. 运行数据库初始化脚本
init-database.bat

# 或者手动执行：
mysql -u root -p
CREATE DATABASE ssm_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE ssm_demo;
source D:/SSMdemo6/src/main/resources/sql/init.sql;
```

### 第三步：项目启动
```bash
# 运行自动启动脚本
start-debug.bat

# 或者手动执行：
mvn clean package
copy target\ssm-demo.war %CATALINA_HOME%\webapps\
%CATALINA_HOME%\bin\startup.bat
```

### 第四步：访问测试
1. **测试页面**: http://localhost:8080/ssm-demo/test.jsp
2. **登录页面**: http://localhost:8080/ssm-demo/login
3. **注册页面**: http://localhost:8080/ssm-demo/register

## 🔧 故障排除

### 问题1：Tomcat启动无输出
**可能原因**：
- CATALINA_HOME未设置
- 端口被占用
- Java环境问题

**解决方案**：
```bash
# 检查环境变量
echo %CATALINA_HOME%
echo %JAVA_HOME%

# 检查端口占用
netstat -ano | findstr :8080

# 手动启动查看错误
%CATALINA_HOME%\bin\catalina.bat run
```

### 问题2：应用部署失败
**可能原因**：
- WAR包损坏
- 权限问题
- 配置错误

**解决方案**：
```bash
# 重新编译打包
mvn clean package -DskipTests

# 检查WAR包
jar -tf target\ssm-demo.war | findstr web.xml

# 清理Tomcat缓存
rmdir /s /q %CATALINA_HOME%\work\Catalina
```

### 问题3：数据库连接失败
**可能原因**：
- MySQL服务未启动
- 数据库不存在
- 连接配置错误

**解决方案**：
```bash
# 检查MySQL服务
net start | findstr MySQL

# 测试数据库连接
mysql -u root -p -e "SHOW DATABASES;"

# 检查配置文件
type src\main\resources\database.properties
```

## 📋 详细启动流程

### 1. 环境准备
```bash
# 设置环境变量（如果未设置）
set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_XXX
set CATALINA_HOME=C:\apache-tomcat-9.0.XX
set PATH=%JAVA_HOME%\bin;%CATALINA_HOME%\bin;%PATH%
```

### 2. 项目编译
```bash
# 进入项目目录
cd D:\SSMdemo6

# 清理编译
mvn clean compile

# 运行测试（可选）
mvn test

# 打包项目
mvn package
```

### 3. 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE ssm_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE ssm_demo;

-- 执行初始化脚本
source src/main/resources/sql/init.sql;

-- 验证数据
SELECT * FROM users;
SELECT * FROM students LIMIT 5;
```

### 4. 应用部署
```bash
# 停止Tomcat（如果正在运行）
%CATALINA_HOME%\bin\shutdown.bat

# 清理旧部署
del %CATALINA_HOME%\webapps\ssm-demo.war
rmdir /s /q %CATALINA_HOME%\webapps\ssm-demo

# 部署新应用
copy target\ssm-demo.war %CATALINA_HOME%\webapps\

# 启动Tomcat
%CATALINA_HOME%\bin\startup.bat
```

### 5. 验证部署
```bash
# 检查Tomcat进程
tasklist | findstr java

# 检查端口监听
netstat -ano | findstr :8080

# 查看日志
type %CATALINA_HOME%\logs\catalina.*.log
```

## 🌐 访问地址

### 主要页面
- **首页**: http://localhost:8080/ssm-demo/
- **测试页**: http://localhost:8080/ssm-demo/test.jsp
- **登录页**: http://localhost:8080/ssm-demo/login
- **注册页**: http://localhost:8080/ssm-demo/register

### 功能页面（需要登录）
- **主页**: http://localhost:8080/ssm-demo/home
- **个人信息**: http://localhost:8080/ssm-demo/profile
- **学生信息**: http://localhost:8080/ssm-demo/students
- **关于我们**: http://localhost:8080/ssm-demo/about

## 👤 测试账号

| 用户名 | 密码 | 说明 |
|--------|------|------|
| admin  | 123456 | 管理员账号 |
| test   | 123456 | 测试账号 |
| demo   | 123456 | 演示账号 |

## 📝 日志查看

### Tomcat日志
```bash
# 启动日志
type %CATALINA_HOME%\logs\catalina.*.log

# 应用日志
type %CATALINA_HOME%\logs\localhost.*.log

# 访问日志
type %CATALINA_HOME%\logs\localhost_access_log.*.txt
```

### 实时日志监控
```bash
# Windows PowerShell
Get-Content %CATALINA_HOME%\logs\catalina.*.log -Wait

# 或使用tail工具
tail -f %CATALINA_HOME%\logs\catalina.*.log
```

## 🔍 常用调试命令

```bash
# 检查Java进程
jps -l

# 检查端口占用
netstat -tulpn | grep :8080

# 检查Tomcat状态
curl -I http://localhost:8080/

# 检查应用状态
curl -I http://localhost:8080/ssm-demo/

# 测试数据库连接
mysql -u root -p -e "USE ssm_demo; SELECT COUNT(*) FROM users;"
```

## 📞 技术支持

如果遇到问题，请按以下顺序检查：

1. **运行测试脚本**: `start-debug.bat`
2. **查看Tomcat日志**: 检查启动错误
3. **验证数据库**: 确保MySQL服务和数据正常
4. **检查网络**: 确保端口未被占用
5. **重新部署**: 清理缓存后重新部署

## 🎯 成功标志

当看到以下内容时，说明启动成功：
- ✅ Tomcat控制台显示"Server startup in XXX ms"
- ✅ 可以访问测试页面
- ✅ 登录功能正常
- ✅ 数据库连接正常
