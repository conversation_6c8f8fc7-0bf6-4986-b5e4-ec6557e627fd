package com.example.ssm.service;

import com.example.ssm.entity.User;
import com.example.ssm.entity.Student;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * 实体类测试 - 简单单元测试
 */
public class EntityTest {

    private User testUser;
    private Student testStudent;

    @Before
    public void setUp() {
        // 创建测试用户
        testUser = new User();
        testUser.setId(1);
        testUser.setUsername("admin");
        testUser.setPassword("123456");
        testUser.setPhone("13800138000");
        testUser.setAvatar("/images/admin-avatar.png");

        // 创建测试学生
        testStudent = new Student();
        testStudent.setId(1);
        testStudent.setStudentId("2021001");
        testStudent.setName("张三");
        testStudent.setGender("男");
        testStudent.setAge(20);
        testStudent.setMajor("计算机科学与技术");
        testStudent.setClassName("计科2101");
        testStudent.setPhone("13800001001");
        testStudent.setEmail("<EMAIL>");
    }

    @Test
    public void testUserEntity() {
        // 测试User实体类的getter和setter方法
        assertEquals("用户ID应该匹配", Integer.valueOf(1), testUser.getId());
        assertEquals("用户名应该匹配", "admin", testUser.getUsername());
        assertEquals("密码应该匹配", "123456", testUser.getPassword());
        assertEquals("手机号应该匹配", "13800138000", testUser.getPhone());
        assertEquals("头像应该匹配", "/images/admin-avatar.png", testUser.getAvatar());

        // 测试toString方法
        String userString = testUser.toString();
        assertNotNull("toString方法不应该返回null", userString);
        assertTrue("toString应该包含用户名", userString.contains("admin"));
    }

    @Test
    public void testUserConstructor() {
        // 测试User的构造方法
        User user1 = new User();
        assertNotNull("默认构造方法应该创建对象", user1);

        User user2 = new User("testuser", "testpass");
        assertEquals("用户名应该匹配", "testuser", user2.getUsername());
        assertEquals("密码应该匹配", "testpass", user2.getPassword());
    }

    @Test
    public void testStudentEntity() {
        // 测试Student实体类的getter和setter方法
        assertEquals("学生ID应该匹配", Integer.valueOf(1), testStudent.getId());
        assertEquals("学号应该匹配", "2021001", testStudent.getStudentId());
        assertEquals("姓名应该匹配", "张三", testStudent.getName());
        assertEquals("性别应该匹配", "男", testStudent.getGender());
        assertEquals("年龄应该匹配", Integer.valueOf(20), testStudent.getAge());
        assertEquals("专业应该匹配", "计算机科学与技术", testStudent.getMajor());
        assertEquals("班级应该匹配", "计科2101", testStudent.getClassName());
        assertEquals("手机号应该匹配", "13800001001", testStudent.getPhone());
        assertEquals("邮箱应该匹配", "<EMAIL>", testStudent.getEmail());

        // 测试toString方法
        String studentString = testStudent.toString();
        assertNotNull("toString方法不应该返回null", studentString);
        assertTrue("toString应该包含学生姓名", studentString.contains("张三"));
    }

    @Test
    public void testStudentConstructor() {
        // 测试Student的构造方法
        Student student1 = new Student();
        assertNotNull("默认构造方法应该创建对象", student1);

        Student student2 = new Student("2021002", "李四", "女", 19, "软件工程", "软工2101");
        assertEquals("学号应该匹配", "2021002", student2.getStudentId());
        assertEquals("姓名应该匹配", "李四", student2.getName());
        assertEquals("性别应该匹配", "女", student2.getGender());
        assertEquals("年龄应该匹配", Integer.valueOf(19), student2.getAge());
        assertEquals("专业应该匹配", "软件工程", student2.getMajor());
        assertEquals("班级应该匹配", "软工2101", student2.getClassName());
    }

    @Test
    public void testUserValidation() {
        // 测试用户数据验证相关的逻辑
        User user = new User();

        // 测试空值处理
        user.setUsername(null);
        assertNull("用户名可以为null", user.getUsername());

        user.setUsername("");
        assertEquals("用户名可以为空字符串", "", user.getUsername());

        // 测试正常值
        user.setUsername("validuser");
        assertEquals("用户名应该正确设置", "validuser", user.getUsername());
    }

    @Test
    public void testStudentValidation() {
        // 测试学生数据验证相关的逻辑
        Student student = new Student();

        // 测试空值处理
        student.setName(null);
        assertNull("姓名可以为null", student.getName());

        student.setAge(null);
        assertNull("年龄可以为null", student.getAge());

        // 测试正常值
        student.setName("测试学生");
        assertEquals("姓名应该正确设置", "测试学生", student.getName());

        student.setAge(18);
        assertEquals("年龄应该正确设置", Integer.valueOf(18), student.getAge());
    }
}
