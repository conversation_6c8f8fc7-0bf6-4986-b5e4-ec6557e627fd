# SSM项目部署指南

## 环境准备

### 1. 软件环境
- **JDK**: 1.8 或更高版本
- **Maven**: 3.6.0 或更高版本
- **MySQL**: 8.0 或更高版本
- **Tomcat**: 9.0 或更高版本
- **IDE**: IntelliJ IDEA 或 Eclipse

### 2. 数据库准备

#### 2.1 安装MySQL
确保MySQL服务正在运行，默认端口为3306。

#### 2.2 创建数据库
```sql
-- 连接到MySQL
mysql -u root -p

-- 执行初始化脚本
source /path/to/src/main/resources/sql/init.sql
```

或者直接在MySQL客户端中执行 `src/main/resources/sql/init.sql` 文件中的SQL语句。

#### 2.3 配置数据库连接
修改 `src/main/resources/database.properties` 文件：
```properties
jdbc.driver=com.mysql.cj.jdbc.Driver
jdbc.url=**********************************************************************************************************************
jdbc.username=你的数据库用户名
jdbc.password=你的数据库密码
```

## 项目构建

### 1. 使用Maven构建
```bash
# 进入项目根目录
cd /path/to/SSMdemo6

# 清理并编译项目
mvn clean compile

# 运行测试
mvn test

# 打包项目
mvn clean package
```

### 2. 使用IDE构建
- 在IntelliJ IDEA中：右键项目 → Maven → Reload project
- 在Eclipse中：右键项目 → Maven → Reload Projects

## 部署方式

### 方式一：Tomcat部署

#### 1. 复制WAR文件
将生成的 `target/ssm-demo-1.0-SNAPSHOT.war` 文件复制到Tomcat的 `webapps` 目录下。

#### 2. 启动Tomcat
```bash
# Linux/Mac
$CATALINA_HOME/bin/startup.sh

# Windows
%CATALINA_HOME%\bin\startup.bat
```

#### 3. 访问应用
打开浏览器访问：`http://localhost:8080/ssm-demo-1.0-SNAPSHOT/`

### 方式二：IDE内置服务器

#### IntelliJ IDEA
1. 配置Tomcat服务器：Run → Edit Configurations → + → Tomcat Server → Local
2. 配置Deployment：添加Artifact → ssm-demo:war exploded
3. 设置Application context：`/ssm-demo`
4. 点击Run运行

#### Eclipse
1. 右键项目 → Run As → Run on Server
2. 选择Tomcat服务器
3. 配置服务器路径和端口

## 配置切换

### XML配置方式（默认）
项目默认使用XML配置方式，相关配置文件：
- `src/main/resources/spring/applicationContext.xml`
- `src/main/resources/spring/dispatcher-servlet.xml`
- `src/main/resources/mybatis/mybatis-config.xml`

### 注解配置方式
要切换到注解配置方式：

1. 修改Controller中的Service注入：
```java
// 在LoginController和RegisterController中
@Qualifier("userAnnotationService") // 改为注解配置的Service
```

2. 修改web.xml或创建WebApplicationInitializer：
```java
// 可以创建一个WebApplicationInitializer来替代web.xml
public class WebAppInitializer implements WebApplicationInitializer {
    @Override
    public void onStartup(ServletContext servletContext) throws ServletException {
        // 配置Spring上下文
        AnnotationConfigWebApplicationContext rootContext = new AnnotationConfigWebApplicationContext();
        rootContext.register(DatabaseConfig.class);
        
        // 配置Spring MVC
        AnnotationConfigWebApplicationContext webContext = new AnnotationConfigWebApplicationContext();
        webContext.register(WebConfig.class);
        
        // 配置DispatcherServlet
        ServletRegistration.Dynamic dispatcher = servletContext.addServlet("dispatcher", 
            new DispatcherServlet(webContext));
        dispatcher.setLoadOnStartup(1);
        dispatcher.addMapping("/");
    }
}
```

## 测试验证

### 1. 功能测试
访问应用后进行以下测试：

#### 登录功能测试
- 使用正确的用户名密码登录（admin/123456）
- 使用错误的用户名密码登录
- 验证错误信息显示和数据回显

#### 注册功能测试
- 注册新用户
- 测试用户名重复验证
- 测试密码确认功能
- 验证表单验证和错误回显

#### 主页功能测试
- 验证导航栏功能
- 测试各个页面跳转
- 验证用户信息显示

#### 学生信息测试
- 查看学生列表
- 测试搜索功能
- 验证搜索结果

### 2. 单元测试
```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=UserServiceTest
```

## 常见问题

### 1. 数据库连接问题
- 检查MySQL服务是否启动
- 验证数据库连接信息是否正确
- 确认数据库和表是否已创建

### 2. 编码问题
- 确保所有文件使用UTF-8编码
- 检查数据库字符集设置
- 验证web.xml中的字符编码过滤器配置

### 3. 依赖问题
- 运行 `mvn clean install` 重新下载依赖
- 检查Maven仓库配置
- 验证网络连接

### 4. 部署问题
- 检查Tomcat版本兼容性
- 验证JDK版本
- 查看Tomcat日志文件

## 日志配置

项目使用Logback进行日志管理，可以在 `src/main/resources` 下创建 `logback.xml` 配置文件：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <logger name="com.example.ssm" level="DEBUG"/>
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.mybatis" level="DEBUG"/>
    
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
```

## 性能优化

### 1. 数据库优化
- 为常用查询字段添加索引
- 使用连接池管理数据库连接
- 优化SQL查询语句

### 2. 应用优化
- 启用Spring缓存
- 使用MyBatis二级缓存
- 优化静态资源加载

### 3. 服务器优化
- 调整Tomcat内存参数
- 配置GZIP压缩
- 使用CDN加速静态资源

## 安全配置

### 1. 密码加密
建议在实际项目中对密码进行加密：
```java
// 使用BCrypt加密密码
String hashedPassword = BCrypt.hashpw(plainPassword, BCrypt.gensalt());
```

### 2. SQL注入防护
- 使用参数化查询
- 验证用户输入
- 使用MyBatis的#{}参数绑定

### 3. XSS防护
- 对用户输入进行HTML转义
- 使用JSTL的c:out标签输出数据
- 配置Content Security Policy

## 监控和维护

### 1. 应用监控
- 配置JMX监控
- 使用Spring Boot Actuator（如果升级到Spring Boot）
- 监控数据库连接池状态

### 2. 日志监控
- 定期检查应用日志
- 配置日志轮转
- 监控错误日志

### 3. 性能监控
- 监控响应时间
- 检查内存使用情况
- 分析SQL执行性能
