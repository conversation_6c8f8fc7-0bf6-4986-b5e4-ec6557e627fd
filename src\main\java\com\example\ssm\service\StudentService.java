package com.example.ssm.service;

import com.example.ssm.entity.Student;
import java.util.List;

/**
 * 学生服务接口
 */
public interface StudentService {
    
    /**
     * 查询所有学生
     * @return 学生列表
     */
    List<Student> findAll();
    
    /**
     * 根据关键词搜索学生
     * @param keyword 搜索关键词
     * @return 学生列表
     */
    List<Student> searchStudents(String keyword);
    
    /**
     * 根据ID查询学生
     * @param id 学生ID
     * @return 学生对象
     */
    Student findById(Integer id);
    
    /**
     * 根据学号查询学生
     * @param studentId 学号
     * @return 学生对象
     */
    Student findByStudentId(String studentId);
    
    /**
     * 添加学生
     * @param student 学生对象
     * @return 添加结果
     */
    boolean addStudent(Student student);
    
    /**
     * 更新学生信息
     * @param student 学生对象
     * @return 更新结果
     */
    boolean updateStudent(Student student);
    
    /**
     * 删除学生
     * @param id 学生ID
     * @return 删除结果
     */
    boolean deleteStudent(Integer id);
    
    /**
     * 分页查询学生
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 学生列表
     */
    List<Student> findByPage(int page, int size);
    
    /**
     * 统计学生总数
     * @return 学生总数
     */
    int countStudents();
}
