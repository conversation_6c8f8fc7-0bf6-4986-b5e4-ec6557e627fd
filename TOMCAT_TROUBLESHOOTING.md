# Tomcat启动问题排查指南

## 问题诊断步骤

### 1. 检查Tomcat安装和配置

#### 验证Tomcat安装
```bash
# 检查Tomcat版本
%CATALINA_HOME%\bin\version.bat

# 或者在Linux/Mac
$CATALINA_HOME/bin/version.sh
```

#### 检查Java环境
```bash
java -version
echo %JAVA_HOME%
```

### 2. 常见启动问题及解决方案

#### 问题1: 端口被占用
**症状**: Tomcat启动失败，提示端口8080被占用
**解决方案**:
```bash
# Windows查看端口占用
netstat -ano | findstr :8080

# 杀死占用进程
taskkill /PID <进程ID> /F

# 或者修改Tomcat端口
# 编辑 %CATALINA_HOME%\conf\server.xml
# 将 <Connector port="8080" 改为其他端口如 8081
```

#### 问题2: 内存不足
**症状**: Tomcat启动缓慢或OutOfMemoryError
**解决方案**:
创建或修改 `%CATALINA_HOME%\bin\setenv.bat`:
```batch
set JAVA_OPTS=-Xms512m -Xmx1024m -XX:PermSize=256m -XX:MaxPermSize=512m
```

#### 问题3: 数据库连接问题
**症状**: 应用启动时Spring初始化失败
**解决方案**:
1. 确保MySQL服务正在运行
2. 验证数据库连接信息
3. 创建数据库和表

### 3. 部署步骤详解

#### 步骤1: 准备WAR包
```bash
# 在项目根目录执行
mvn clean package

# 确认生成了 target/ssm-demo.war
```

#### 步骤2: 部署到Tomcat
```bash
# 方法1: 复制WAR包
copy target\ssm-demo.war %CATALINA_HOME%\webapps\

# 方法2: 重命名为ROOT.war (作为根应用)
copy target\ssm-demo.war %CATALINA_HOME%\webapps\ROOT.war
```

#### 步骤3: 启动Tomcat
```bash
# Windows
%CATALINA_HOME%\bin\startup.bat

# Linux/Mac
$CATALINA_HOME/bin/startup.sh
```

### 4. 数据库准备

#### 创建数据库
```sql
-- 连接MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE ssm_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE ssm_demo;

-- 执行初始化脚本
source D:/SSMdemo6/src/main/resources/sql/init.sql;
```

#### 验证数据库
```sql
-- 检查表是否创建成功
SHOW TABLES;

-- 检查用户数据
SELECT * FROM users;

-- 检查学生数据
SELECT * FROM students LIMIT 5;
```

### 5. 日志查看和调试

#### Tomcat日志位置
- **启动日志**: `%CATALINA_HOME%\logs\catalina.out` (Linux/Mac) 或 `%CATALINA_HOME%\logs\catalina.yyyy-mm-dd.log` (Windows)
- **应用日志**: `%CATALINA_HOME%\logs\localhost.yyyy-mm-dd.log`
- **访问日志**: `%CATALINA_HOME%\logs\localhost_access_log.yyyy-mm-dd.txt`

#### 查看日志命令
```bash
# Windows
type %CATALINA_HOME%\logs\catalina.2024-06-02.log

# Linux/Mac
tail -f $CATALINA_HOME/logs/catalina.out
```

### 6. 常见错误及解决方案

#### 错误1: ClassNotFoundException
**原因**: 缺少依赖的JAR包
**解决方案**: 
1. 检查pom.xml依赖
2. 重新编译: `mvn clean compile`
3. 确保WAR包中包含所有依赖

#### 错误2: Spring配置错误
**原因**: Spring配置文件路径或内容错误
**解决方案**:
1. 检查web.xml中的配置路径
2. 验证Spring配置文件语法
3. 检查包扫描路径

#### 错误3: 数据库连接失败
**原因**: 数据库服务未启动或连接配置错误
**解决方案**:
1. 启动MySQL服务
2. 检查database.properties配置
3. 验证数据库用户权限

### 7. 测试访问

#### 访问地址
- **根路径**: `http://localhost:8080/ssm-demo/`
- **登录页面**: `http://localhost:8080/ssm-demo/login`
- **注册页面**: `http://localhost:8080/ssm-demo/register`

#### 测试账号
- 用户名: admin, 密码: 123456
- 用户名: test, 密码: 123456
- 用户名: demo, 密码: 123456

### 8. IDE中运行Tomcat

#### IntelliJ IDEA配置
1. Run → Edit Configurations
2. 添加 Tomcat Server → Local
3. 配置Tomcat安装路径
4. 在Deployment标签页添加Artifact
5. 设置Application context: `/ssm-demo`

#### Eclipse配置
1. 右键项目 → Run As → Run on Server
2. 选择Tomcat服务器
3. 配置服务器路径和端口

### 9. 快速诊断命令

#### 检查服务状态
```bash
# 检查Tomcat进程
tasklist | findstr java

# 检查端口监听
netstat -an | findstr :8080

# 检查MySQL服务
net start | findstr MySQL
```

#### 快速重启
```bash
# 停止Tomcat
%CATALINA_HOME%\bin\shutdown.bat

# 等待几秒后启动
%CATALINA_HOME%\bin\startup.bat
```

### 10. 故障排除清单

- [ ] Java环境配置正确 (JAVA_HOME)
- [ ] Tomcat安装完整 (CATALINA_HOME)
- [ ] 端口8080未被占用
- [ ] MySQL服务正在运行
- [ ] 数据库ssm_demo已创建
- [ ] 数据库表已初始化
- [ ] WAR包部署到webapps目录
- [ ] 项目编译无错误
- [ ] 依赖包完整

### 11. 应急解决方案

如果仍然无法启动，可以尝试：

1. **使用内嵌Tomcat**:
   添加Spring Boot依赖，使用内嵌服务器

2. **使用其他服务器**:
   尝试Jetty或其他Servlet容器

3. **简化配置**:
   临时移除复杂配置，逐步添加功能

4. **检查防火墙**:
   确保防火墙没有阻止8080端口
