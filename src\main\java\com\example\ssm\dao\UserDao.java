package com.example.ssm.dao;

import com.example.ssm.entity.User;
import org.apache.ibatis.annotations.Param;

/**
 * 用户数据访问接口 - XML配置方式
 */
public interface UserDao {
    
    /**
     * 根据用户名查询用户
     * @param username 用户名
     * @return 用户对象
     */
    User findByUsername(@Param("username") String username);
    
    /**
     * 根据用户名和密码查询用户（登录验证）
     * @param username 用户名
     * @param password 密码
     * @return 用户对象
     */
    User findByUsernameAndPassword(@Param("username") String username, @Param("password") String password);
    
    /**
     * 插入新用户
     * @param user 用户对象
     * @return 影响的行数
     */
    int insertUser(User user);
    
    /**
     * 根据ID查询用户
     * @param id 用户ID
     * @return 用户对象
     */
    User findById(@Param("id") Integer id);
    
    /**
     * 更新用户信息
     * @param user 用户对象
     * @return 影响的行数
     */
    int updateUser(User user);
    
    /**
     * 删除用户
     * @param id 用户ID
     * @return 影响的行数
     */
    int deleteUser(@Param("id") Integer id);
}
