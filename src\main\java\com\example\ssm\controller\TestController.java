package com.example.ssm.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器 - 用于验证Spring MVC配置
 */
@Controller
public class TestController {
    
    /**
     * 简单的测试接口
     */
    @GetMapping("/test")
    @ResponseBody
    public String test() {
        return "SSM框架配置正常！当前时间：" + new java.util.Date();
    }
    
    /**
     * JSON测试接口
     */
    @GetMapping("/test/json")
    @ResponseBody
    public Map<String, Object> testJson() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "success");
        result.put("message", "Spring MVC JSON响应正常");
        result.put("timestamp", System.currentTimeMillis());
        result.put("framework", "Spring + Spring MVC + MyBatis");
        return result;
    }
    
    /**
     * 系统信息接口
     */
    @GetMapping("/test/info")
    @ResponseBody
    public Map<String, Object> systemInfo(HttpServletRequest request) {
        Map<String, Object> info = new HashMap<>();
        
        // 系统信息
        info.put("javaVersion", System.getProperty("java.version"));
        info.put("osName", System.getProperty("os.name"));
        info.put("osVersion", System.getProperty("os.version"));
        
        // 应用信息
        info.put("contextPath", request.getContextPath());
        info.put("serverName", request.getServerName());
        info.put("serverPort", request.getServerPort());
        info.put("requestURL", request.getRequestURL().toString());
        
        // 时间信息
        info.put("currentTime", new java.util.Date());
        info.put("timestamp", System.currentTimeMillis());
        
        return info;
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    @ResponseBody
    public Map<String, Object> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("application", "SSM Demo");
        health.put("version", "1.0.0");
        health.put("timestamp", new java.util.Date());
        
        // 检查各组件状态
        Map<String, String> components = new HashMap<>();
        components.put("spring", "UP");
        components.put("springMvc", "UP");
        components.put("tomcat", "UP");
        
        // 简单的数据库连接检查（这里先标记为未知）
        components.put("database", "UNKNOWN");
        
        health.put("components", components);
        
        return health;
    }
    
    /**
     * 错误测试接口
     */
    @GetMapping("/test/error")
    @ResponseBody
    public String testError() {
        throw new RuntimeException("这是一个测试异常，用于验证错误处理");
    }
}
