package com.example.ssm.dao;

import com.example.ssm.entity.Student;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 学生数据访问接口 - XML配置方式
 */
public interface StudentDao {
    
    /**
     * 查询所有学生
     * @return 学生列表
     */
    List<Student> findAll();
    
    /**
     * 根据关键词搜索学生
     * @param keyword 搜索关键词
     * @return 学生列表
     */
    List<Student> searchStudents(@Param("keyword") String keyword);
    
    /**
     * 根据ID查询学生
     * @param id 学生ID
     * @return 学生对象
     */
    Student findById(@Param("id") Integer id);
    
    /**
     * 根据学号查询学生
     * @param studentId 学号
     * @return 学生对象
     */
    Student findByStudentId(@Param("studentId") String studentId);
    
    /**
     * 插入新学生
     * @param student 学生对象
     * @return 影响的行数
     */
    int insertStudent(Student student);
    
    /**
     * 更新学生信息
     * @param student 学生对象
     * @return 影响的行数
     */
    int updateStudent(Student student);
    
    /**
     * 删除学生
     * @param id 学生ID
     * @return 影响的行数
     */
    int deleteStudent(@Param("id") Integer id);
    
    /**
     * 分页查询学生
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 学生列表
     */
    List<Student> findByPage(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 统计学生总数
     * @return 学生总数
     */
    int countStudents();
}
