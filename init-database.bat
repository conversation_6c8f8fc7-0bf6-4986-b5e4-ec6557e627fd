@echo off
echo ========================================
echo 数据库初始化脚本
echo ========================================

echo.
echo 请确保MySQL服务已启动...
net start | findstr MySQL
if %errorlevel% neq 0 (
    echo 警告: MySQL服务可能未启动
    echo 请手动启动MySQL服务
)

echo.
echo 请输入MySQL root用户密码:
set /p mysql_password=密码: 

echo.
echo 正在连接MySQL并创建数据库...

mysql -u root -p%mysql_password% -e "CREATE DATABASE IF NOT EXISTS ssm_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if %errorlevel% neq 0 (
    echo 错误: 数据库创建失败，请检查MySQL连接
    pause
    exit /b 1
)

echo 数据库创建成功！

echo.
echo 正在初始化数据库表和数据...
mysql -u root -p%mysql_password% ssm_demo < src\main\resources\sql\init.sql
if %errorlevel% neq 0 (
    echo 错误: 数据库初始化失败
    pause
    exit /b 1
)

echo 数据库初始化成功！

echo.
echo 验证数据库...
mysql -u root -p%mysql_password% -e "USE ssm_demo; SHOW TABLES; SELECT COUNT(*) as user_count FROM users; SELECT COUNT(*) as student_count FROM students;"

echo.
echo ========================================
echo 数据库初始化完成！
echo ========================================
echo.
echo 数据库信息:
echo 数据库名: ssm_demo
echo 用户表: users (包含测试用户)
echo 学生表: students (包含测试数据)
echo.
echo 测试账号:
echo admin/123456
echo test/123456  
echo demo/123456
echo.
pause
