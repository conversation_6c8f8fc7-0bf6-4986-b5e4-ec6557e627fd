<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.ssm.dao.UserDao">
    
    <!-- 结果映射 -->
    <resultMap id="UserResultMap" type="User">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="phone" column="phone"/>
        <result property="avatar" column="avatar"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    
    <!-- 根据用户名查询用户 -->
    <select id="findByUsername" parameterType="string" resultMap="UserResultMap">
        SELECT * FROM users WHERE username = #{username}
    </select>
    
    <!-- 根据用户名和密码查询用户（登录验证） -->
    <select id="findByUsernameAndPassword" resultMap="UserResultMap">
        SELECT * FROM users 
        WHERE username = #{username} AND password = #{password}
    </select>
    
    <!-- 插入新用户 -->
    <insert id="insertUser" parameterType="User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users(username, password, phone, avatar, create_time, update_time)
        VALUES(#{username}, #{password}, #{phone}, #{avatar}, NOW(), NOW())
    </insert>
    
    <!-- 根据ID查询用户 -->
    <select id="findById" parameterType="int" resultMap="UserResultMap">
        SELECT * FROM users WHERE id = #{id}
    </select>
    
    <!-- 更新用户信息 -->
    <update id="updateUser" parameterType="User">
        UPDATE users 
        SET username = #{username}, 
            password = #{password}, 
            phone = #{phone}, 
            avatar = #{avatar}, 
            update_time = NOW()
        WHERE id = #{id}
    </update>
    
    <!-- 删除用户 -->
    <delete id="deleteUser" parameterType="int">
        DELETE FROM users WHERE id = #{id}
    </delete>
    
</mapper>
