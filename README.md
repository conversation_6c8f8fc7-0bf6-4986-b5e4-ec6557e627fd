# SSM框架演示项目

这是一个基于Spring + Spring MVC + MyBatis（SSM）框架开发的演示项目，实现了用户登录、注册和学生信息管理功能。

## 项目特点

- **双配置方式**：同时支持XML配置和注解配置两种方式
- **完整功能**：包含登录、注册、主页、学生信息管理等功能
- **响应式设计**：使用Bootstrap 5构建美观的响应式界面
- **数据验证**：前端和后端双重数据验证
- **会话管理**：完整的用户会话和权限控制

## 技术栈

### 后端技术
- **Spring Framework 5.3.21** - 核心框架
- **Spring MVC** - Web框架
- **MyBatis 3.5.10** - 持久层框架
- **MySQL 8.0** - 数据库
- **Druid** - 数据库连接池
- **Maven** - 项目管理工具

### 前端技术
- **Bootstrap 5.1.3** - UI框架
- **Font Awesome 6.0** - 图标库
- **JSP + JSTL** - 视图技术
- **jQuery** - JavaScript库

## 项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/example/ssm/
│   │       ├── controller/     # 控制器层
│   │       ├── service/        # 服务层
│   │       ├── dao/           # 数据访问层
│   │       ├── entity/        # 实体类
│   │       ├── config/        # 配置类（注解方式）
│   │       └── interceptor/   # 拦截器
│   ├── resources/
│   │   ├── spring/           # Spring配置文件（XML方式）
│   │   ├── mybatis/          # MyBatis配置文件
│   │   ├── sql/              # 数据库脚本
│   │   └── database.properties # 数据库配置
│   └── webapp/
│       ├── WEB-INF/
│       │   ├── views/        # JSP页面
│       │   └── web.xml       # Web配置
│       └── index.jsp         # 首页
```

## 功能模块

### 1. 用户登录
- 用户名和密码验证
- 登录失败时回显用户输入
- 会话管理和权限控制

### 2. 用户注册
- 表单数据验证（前端+后端）
- 密码确认验证
- 用户名重复检查
- 注册成功后自动填充登录表单

### 3. 主页设计
- 响应式导航栏
- 用户信息展示
- 功能模块入口
- 美观的卡片式布局

### 4. 学生信息管理
- 学生列表展示
- 关键词搜索功能
- 支持按姓名、学号、专业、班级搜索

### 5. 个人信息
- 用户基本信息展示
- 头像显示
- 注册时间等详细信息

## 配置方式

### XML配置方式
- `applicationContext.xml` - Spring核心配置
- `dispatcher-servlet.xml` - Spring MVC配置
- `mybatis-config.xml` - MyBatis配置
- `UserMapper.xml` / `StudentMapper.xml` - SQL映射文件

### 注解配置方式
- `DatabaseConfig.java` - 数据库配置类
- `WebConfig.java` - Web配置类
- `@Mapper` 注解的DAO接口
- `@Service` 注解的服务类

## 快速开始

### 1. 环境要求
- JDK 8+
- MySQL 8.0+
- Maven 3.6+
- Tomcat 9.0+

### 2. 数据库配置
1. 创建MySQL数据库
2. 执行 `src/main/resources/sql/init.sql` 脚本
3. 修改 `src/main/resources/database.properties` 中的数据库连接信息

### 3. 运行项目
1. 使用Maven编译项目：`mvn clean compile`
2. 部署到Tomcat服务器
3. 访问：`http://localhost:8080/项目名/`

### 4. 测试账号
- 用户名：admin，密码：123456
- 用户名：test，密码：123456
- 用户名：demo，密码：123456

## 切换配置方式

### 使用XML配置（默认）
在 `LoginController` 和 `RegisterController` 中：
```java
@Qualifier("userService") // XML配置方式
```

### 使用注解配置
在 `LoginController` 和 `RegisterController` 中：
```java
@Qualifier("userAnnotationService") // 注解配置方式
```

## 项目截图

### 登录页面
- 美观的渐变背景
- 表单验证和错误提示
- 响应式设计

### 注册页面
- 完整的表单验证
- 密码确认功能
- 错误信息回显

### 主页面
- 现代化的导航栏
- 卡片式功能模块
- 用户信息展示

### 学生信息页面
- 搜索功能
- 表格展示
- 响应式布局

## 开发说明

这个项目是为了演示SSM框架的整合和使用而开发的，包含了Web开发中常见的功能模块。项目代码结构清晰，注释详细，适合学习和参考。

## 许可证

MIT License
