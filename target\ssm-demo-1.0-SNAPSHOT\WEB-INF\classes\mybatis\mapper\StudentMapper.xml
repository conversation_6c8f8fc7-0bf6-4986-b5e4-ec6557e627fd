<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.ssm.dao.StudentDao">
    
    <!-- 结果映射 -->
    <resultMap id="StudentResultMap" type="Student">
        <id property="id" column="id"/>
        <result property="studentId" column="student_id"/>
        <result property="name" column="name"/>
        <result property="gender" column="gender"/>
        <result property="age" column="age"/>
        <result property="major" column="major"/>
        <result property="className" column="class_name"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    
    <!-- 查询所有学生 -->
    <select id="findAll" resultMap="StudentResultMap">
        SELECT * FROM students ORDER BY create_time DESC
    </select>
    
    <!-- 根据关键词搜索学生 -->
    <select id="searchStudents" parameterType="string" resultMap="StudentResultMap">
        SELECT * FROM students 
        WHERE name LIKE CONCAT('%', #{keyword}, '%')
           OR student_id LIKE CONCAT('%', #{keyword}, '%')
           OR major LIKE CONCAT('%', #{keyword}, '%')
           OR class_name LIKE CONCAT('%', #{keyword}, '%')
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据ID查询学生 -->
    <select id="findById" parameterType="int" resultMap="StudentResultMap">
        SELECT * FROM students WHERE id = #{id}
    </select>
    
    <!-- 根据学号查询学生 -->
    <select id="findByStudentId" parameterType="string" resultMap="StudentResultMap">
        SELECT * FROM students WHERE student_id = #{studentId}
    </select>
    
    <!-- 插入新学生 -->
    <insert id="insertStudent" parameterType="Student" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO students(student_id, name, gender, age, major, class_name, phone, email, create_time, update_time)
        VALUES(#{studentId}, #{name}, #{gender}, #{age}, #{major}, #{className}, #{phone}, #{email}, NOW(), NOW())
    </insert>
    
    <!-- 更新学生信息 -->
    <update id="updateStudent" parameterType="Student">
        UPDATE students 
        SET student_id = #{studentId}, 
            name = #{name}, 
            gender = #{gender}, 
            age = #{age}, 
            major = #{major}, 
            class_name = #{className}, 
            phone = #{phone}, 
            email = #{email}, 
            update_time = NOW()
        WHERE id = #{id}
    </update>
    
    <!-- 删除学生 -->
    <delete id="deleteStudent" parameterType="int">
        DELETE FROM students WHERE id = #{id}
    </delete>
    
    <!-- 分页查询学生 -->
    <select id="findByPage" resultMap="StudentResultMap">
        SELECT * FROM students 
        ORDER BY create_time DESC 
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 统计学生总数 -->
    <select id="countStudents" resultType="int">
        SELECT COUNT(*) FROM students
    </select>
    
</mapper>
