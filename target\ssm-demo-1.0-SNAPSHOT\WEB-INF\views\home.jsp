<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主页 - SSM Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        .nav-link {
            font-weight: 500;
            margin: 0 10px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        .main-content {
            margin-top: 20px;
            min-height: calc(100vh - 120px);
        }
        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .info-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            transition: transform 0.3s ease;
        }
        .info-card:hover {
            transform: translateY(-5px);
        }
        .student-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        .search-box {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .btn-search {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
        }
        .avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid rgba(255,255,255,0.3);
        }
        .footer {
            background-color: #343a40;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/home">
                <i class="fas fa-graduation-cap"></i> SSM Demo
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link ${activeTab == 'home' ? 'active' : ''}" href="${pageContext.request.contextPath}/home">
                            <i class="fas fa-home"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${activeTab == 'profile' ? 'active' : ''}" href="${pageContext.request.contextPath}/profile">
                            <i class="fas fa-user"></i> 个人信息
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${activeTab == 'students' ? 'active' : ''}" href="${pageContext.request.contextPath}/students">
                            <i class="fas fa-users"></i> 学生信息
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${activeTab == 'about' ? 'active' : ''}" href="${pageContext.request.contextPath}/about">
                            <i class="fas fa-info-circle"></i> 关于我们
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <img src="${currentUser.avatar != null ? currentUser.avatar : '/images/default-avatar.png'}" 
                                 alt="头像" class="avatar me-2" style="width: 30px; height: 30px;">
                            ${currentUser.username}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="${pageContext.request.contextPath}/profile">
                                <i class="fas fa-user"></i> 个人信息
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="${pageContext.request.contextPath}/logout">
                                <i class="fas fa-sign-out-alt"></i> 退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container main-content">
        <!-- 首页内容 -->
        <c:if test="${activeTab == 'home' || empty activeTab}">
            <div class="welcome-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2><i class="fas fa-hand-wave"></i> 欢迎回来，${currentUser.username}！</h2>
                        <p class="mb-0">这是一个基于SSM框架开发的演示系统，展示了登录、注册和学生信息管理功能。</p>
                    </div>
                    <div class="col-md-4 text-center">
                        <img src="${currentUser.avatar != null ? currentUser.avatar : '/images/default-avatar.png'}" 
                             alt="用户头像" class="avatar" style="width: 100px; height: 100px;">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="info-card text-center">
                        <i class="fas fa-users fa-3x text-primary mb-3"></i>
                        <h5>学生管理</h5>
                        <p class="text-muted">查看和搜索学生信息</p>
                        <a href="${pageContext.request.contextPath}/students" class="btn btn-primary">
                            <i class="fas fa-arrow-right"></i> 进入
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="info-card text-center">
                        <i class="fas fa-user-cog fa-3x text-success mb-3"></i>
                        <h5>个人中心</h5>
                        <p class="text-muted">管理个人信息和设置</p>
                        <a href="${pageContext.request.contextPath}/profile" class="btn btn-success">
                            <i class="fas fa-arrow-right"></i> 进入
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="info-card text-center">
                        <i class="fas fa-info-circle fa-3x text-info mb-3"></i>
                        <h5>系统信息</h5>
                        <p class="text-muted">了解系统功能和技术</p>
                        <a href="${pageContext.request.contextPath}/about" class="btn btn-info">
                            <i class="fas fa-arrow-right"></i> 进入
                        </a>
                    </div>
                </div>
            </div>
        </c:if>

        <!-- 个人信息页面 -->
        <c:if test="${activeTab == 'profile'}">
            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="info-card">
                        <h3><i class="fas fa-user"></i> 个人信息</h3>
                        <hr>
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <img src="${currentUser.avatar != null ? currentUser.avatar : '/images/default-avatar.png'}" 
                                     alt="用户头像" class="avatar" style="width: 120px; height: 120px;">
                            </div>
                            <div class="col-md-8">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>用户名：</strong></td>
                                        <td>${currentUser.username}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>手机号：</strong></td>
                                        <td>${currentUser.phone != null ? currentUser.phone : '未设置'}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>注册时间：</strong></td>
                                        <td><fmt:formatDate value="${currentUser.createTime}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                                    </tr>
                                    <tr>
                                        <td><strong>最后更新：</strong></td>
                                        <td><fmt:formatDate value="${currentUser.updateTime}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </c:if>

        <!-- 学生信息页面 -->
        <c:if test="${activeTab == 'students'}">
            <div class="search-box">
                <h4><i class="fas fa-search"></i> 学生信息搜索</h4>
                <form action="${pageContext.request.contextPath}/students" method="get" class="row g-3">
                    <div class="col-md-10">
                        <input type="text" class="form-control" name="keyword" value="${keyword}" 
                               placeholder="请输入学生姓名、学号、专业或班级进行搜索...">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-search text-white w-100">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                </form>
            </div>
            
            <div class="student-table">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>学号</th>
                                <th>姓名</th>
                                <th>性别</th>
                                <th>年龄</th>
                                <th>专业</th>
                                <th>班级</th>
                                <th>联系方式</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:choose>
                                <c:when test="${not empty students}">
                                    <c:forEach var="student" items="${students}">
                                        <tr>
                                            <td>${student.studentId}</td>
                                            <td>${student.name}</td>
                                            <td>${student.gender}</td>
                                            <td>${student.age}</td>
                                            <td>${student.major}</td>
                                            <td>${student.className}</td>
                                            <td>${student.phone != null ? student.phone : '未设置'}</td>
                                        </tr>
                                    </c:forEach>
                                </c:when>
                                <c:otherwise>
                                    <tr>
                                        <td colspan="7" class="text-center text-muted py-4">
                                            <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                            暂无学生信息
                                        </td>
                                    </tr>
                                </c:otherwise>
                            </c:choose>
                        </tbody>
                    </table>
                </div>
            </div>
        </c:if>

        <!-- 关于我们页面 -->
        <c:if test="${activeTab == 'about'}">
            <div class="row">
                <div class="col-md-10 mx-auto">
                    <div class="info-card">
                        <h3><i class="fas fa-info-circle"></i> 关于系统</h3>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-cogs"></i> 技术架构</h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> Spring Framework 5.3.21</li>
                                    <li><i class="fas fa-check text-success"></i> Spring MVC</li>
                                    <li><i class="fas fa-check text-success"></i> MyBatis 3.5.10</li>
                                    <li><i class="fas fa-check text-success"></i> MySQL 8.0</li>
                                    <li><i class="fas fa-check text-success"></i> Bootstrap 5.1.3</li>
                                    <li><i class="fas fa-check text-success"></i> Druid 连接池</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5><i class="fas fa-star"></i> 主要功能</h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-primary"></i> 用户登录验证</li>
                                    <li><i class="fas fa-check text-primary"></i> 用户注册功能</li>
                                    <li><i class="fas fa-check text-primary"></i> 学生信息管理</li>
                                    <li><i class="fas fa-check text-primary"></i> 关键词搜索</li>
                                    <li><i class="fas fa-check text-primary"></i> 响应式设计</li>
                                    <li><i class="fas fa-check text-primary"></i> 会话管理</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h5><i class="fas fa-code"></i> 配置方式</h5>
                            <p>本系统同时支持两种配置方式：</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title"><i class="fas fa-file-code"></i> XML配置方式</h6>
                                            <p class="card-text">使用传统的XML配置文件进行Spring和MyBatis的配置。</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title"><i class="fas fa-at"></i> 注解配置方式</h6>
                                            <p class="card-text">使用Java注解进行配置，代码更加简洁现代。</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </c:if>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 SSM Demo. 基于Spring + Spring MVC + MyBatis框架开发.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
