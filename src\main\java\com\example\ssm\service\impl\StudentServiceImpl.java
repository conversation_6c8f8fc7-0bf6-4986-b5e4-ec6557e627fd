package com.example.ssm.service.impl;

import com.example.ssm.dao.StudentDao;
import com.example.ssm.entity.Student;
import com.example.ssm.service.StudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

/**
 * 学生服务实现类 - XML配置方式
 */
@Service("studentService")
@Transactional
public class StudentServiceImpl implements StudentService {
    
    @Autowired
    private StudentDao studentDao;
    
    @Override
    public List<Student> findAll() {
        return studentDao.findAll();
    }
    
    @Override
    public List<Student> searchStudents(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return findAll();
        }
        return studentDao.searchStudents(keyword.trim());
    }
    
    @Override
    public Student findById(Integer id) {
        if (id == null) {
            return null;
        }
        return studentDao.findById(id);
    }
    
    @Override
    public Student findByStudentId(String studentId) {
        if (studentId == null || studentId.trim().isEmpty()) {
            return null;
        }
        return studentDao.findByStudentId(studentId.trim());
    }
    
    @Override
    public boolean addStudent(Student student) {
        if (student == null || student.getStudentId() == null || student.getName() == null) {
            return false;
        }
        
        // 检查学号是否已存在
        if (findByStudentId(student.getStudentId()) != null) {
            return false;
        }
        
        try {
            int result = studentDao.insertStudent(student);
            return result > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public boolean updateStudent(Student student) {
        if (student == null || student.getId() == null) {
            return false;
        }
        
        try {
            int result = studentDao.updateStudent(student);
            return result > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public boolean deleteStudent(Integer id) {
        if (id == null) {
            return false;
        }
        
        try {
            int result = studentDao.deleteStudent(id);
            return result > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public List<Student> findByPage(int page, int size) {
        if (page < 1 || size < 1) {
            return findAll();
        }
        
        int offset = (page - 1) * size;
        return studentDao.findByPage(offset, size);
    }
    
    @Override
    public int countStudents() {
        return studentDao.countStudents();
    }
}
