<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>SSM项目测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>SSM项目测试页面</h1>
    
    <div class="section">
        <h2>基本信息</h2>
        <p><strong>当前时间:</strong> <%= new java.util.Date() %></p>
        <p><strong>服务器信息:</strong> <%= application.getServerInfo() %></p>
        <p><strong>项目路径:</strong> <%= request.getContextPath() %></p>
        <p><strong>会话ID:</strong> <%= session.getId() %></p>
    </div>
    
    <div class="section">
        <h2>环境检查</h2>
        <p><strong>Java版本:</strong> <%= System.getProperty("java.version") %></p>
        <p><strong>操作系统:</strong> <%= System.getProperty("os.name") %></p>
        <p><strong>字符编码:</strong> <%= request.getCharacterEncoding() %></p>
        <p><strong>响应编码:</strong> <%= response.getCharacterEncoding() %></p>
    </div>
    
    <div class="section">
        <h2>功能链接</h2>
        <p><a href="<%= request.getContextPath() %>/login">登录页面</a></p>
        <p><a href="<%= request.getContextPath() %>/register">注册页面</a></p>
        <p><a href="<%= request.getContextPath() %>/home">主页 (需要登录)</a></p>
    </div>
    
    <div class="section">
        <h2>测试账号</h2>
        <ul>
            <li>用户名: admin, 密码: 123456</li>
            <li>用户名: test, 密码: 123456</li>
            <li>用户名: demo, 密码: 123456</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>中文测试</h2>
        <p>这是中文字符测试：你好世界！</p>
        <p>特殊字符测试：©®™€£¥</p>
    </div>
    
    <script>
        // 简单的JavaScript测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            
            // 添加状态指示
            var statusDiv = document.createElement('div');
            statusDiv.className = 'section';
            statusDiv.innerHTML = '<h2>状态检查</h2><p class="success">✓ JSP页面正常工作</p><p class="success">✓ JavaScript正常执行</p>';
            document.body.appendChild(statusDiv);
        });
    </script>
</body>
</html>
